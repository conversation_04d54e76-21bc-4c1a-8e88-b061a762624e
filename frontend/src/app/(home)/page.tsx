'use client';

import { useEffect, useState } from 'react';
import { CTASection } from '@/components/home/<USER>/cta-section';
// import { FAQSection } from "@/components/sections/faq-section";
import { FooterSection } from '@/components/home/<USER>/footer-section';
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { OpenSourceSection } from '@/components/home/<USER>/open-source-section';
import { PricingSection } from '@/components/home/<USER>/pricing-section';
import { UseCasesSection } from '@/components/home/<USER>/use-cases-section';
import { ModalProviders } from '@/providers/modal-providers';
import { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';
import { BackgroundAALChecker } from '@/components/auth/background-aal-checker';
import { BentoSection } from '@/components/home/<USER>/bento-section';
import { CompanyShowcase } from '@/components/home/<USER>/company-showcase';
import { FeatureSection } from '@/components/home/<USER>/feature-section';
import { QuoteSection } from '@/components/home/<USER>/quote-section';
import { TestimonialSection } from '@/components/home/<USER>/testimonial-section';
import { FAQSection } from '@/components/home/<USER>/faq-section';
import { AgentShowcaseSection } from '@/components/home/<USER>/agent-showcase-section';

export default function Home() {
  return (
    <>
      <ModalProviders />
      <BackgroundAALChecker>
        <main className="flex flex-col items-center justify-center min-h-screen w-full">
          <div className="w-full divide-y divide-border">
            <HeroSection />
            <BentoSection />
            {/* <AgentShowcaseSection /> */}
            <OpenSourceSection />
            <PricingSection />
            {/* <TestimonialSection /> */}
            {/* <FAQSection /> */}
            <CTASection />
            <FooterSection />
          </div>
        </main>
      </BackgroundAALChecker>
    </>
  );
}
